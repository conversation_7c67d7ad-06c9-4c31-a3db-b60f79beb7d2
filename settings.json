{"selectedAuthType": "qwen-o<PERSON>h", "mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"]}, "shadcn-ui-mcp": {"command": "npx", "args": ["@heilgar/shadcn-ui-mcp-server"]}}, "shadcn-ui-mcp-server": {"command": "npx", "args": ["@jpisnice/shadcn-ui-mcp-server"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"}}}